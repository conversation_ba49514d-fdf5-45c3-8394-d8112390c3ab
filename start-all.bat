@echo off
title Main Microservice Launcher

echo Starting all microservices...

REM Start API Gateway
start "API Gateway" cmd /c "cd api-gateway && npm start"

REM Start Analysis Worker
start "Analysis Worker" cmd /c "cd analysis-worker && npm start"

REM Start Archive Service
start "Archive Service" cmd /c "cd archive-service && npm start"

REM Start Assessment Service
start "Assessment Service" cmd /c "cd assessment-service && npm start"

REM Start Auth Service
start "Auth Service" cmd /c "cd auth-service && npm start"

REM Start Notification Service
start "Notification Service" cmd /c "cd notification-service && npm start"

echo All services have been launched in separate windows.
